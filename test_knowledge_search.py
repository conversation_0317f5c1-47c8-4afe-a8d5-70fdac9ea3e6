"""
测试知识库搜索功能
"""
from services.knowledge_base import knowledge_base_service

def test_search_with_different_thresholds():
    """使用不同的相似度阈值测试搜索"""
    print("=== 测试知识库搜索功能 ===\n")
    
    # 获取知识库统计信息
    stats = knowledge_base_service.get_knowledge_stats()
    print(f"📊 当前知识库状态:")
    print(f"- 总文档数: {stats['vector_store']['count']}")
    print(f"- 存储位置: {stats['vector_store']['persist_directory']}")
    print()
    
    # 测试查询
    test_queries = [
        "原神",
        "王者荣耀",
        "射手",
        "建筑",
        "攻略",
        "新手",
        "技巧"
    ]
    
    # 不同的相似度阈值
    thresholds = [0.0, 0.3, 0.5, 0.7]
    
    for query in test_queries:
        print(f"🔍 搜索关键词: '{query}'")
        
        for threshold in thresholds:
            print(f"\n  相似度阈值: {threshold}")
            results = knowledge_base_service.search_knowledge(
                query=query, 
                top_k=3, 
                similarity_threshold=threshold
            )
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"    结果{i}: {result['document'][:60]}...")
                    print(f"    相似度: {result['similarity']:.3f}")
                    print(f"    来源: {result['metadata'].get('source', '未知')}")
            else:
                print(f"    无结果")
        
        print("-" * 50)

def test_direct_vector_search():
    """直接测试向量数据库搜索"""
    print("\n=== 直接测试向量数据库 ===\n")
    
    try:
        # 直接使用向量存储进行搜索
        vector_store = knowledge_base_service.vector_store
        
        # 获取集合信息
        collection_info = vector_store.get_collection_info()
        print(f"集合信息: {collection_info}")
        
        # 测试搜索
        test_query = "游戏"
        print(f"\n测试查询: '{test_query}'")
        
        results = vector_store.search_similar(test_query, n_results=5)
        
        if results:
            print(f"找到 {len(results)} 个结果:")
            for i, result in enumerate(results, 1):
                print(f"  结果{i}:")
                print(f"    内容: {result['document'][:100]}...")
                print(f"    距离: {result['distance']:.3f}")
                print(f"    相似度: {result['similarity']:.3f}")
                print(f"    元数据: {result['metadata']}")
                print()
        else:
            print("未找到任何结果")
            
    except Exception as e:
        print(f"直接搜索测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_search_with_different_thresholds()
    test_direct_vector_search()

if __name__ == "__main__":
    main()
