<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏攻略小助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h4><i class="bi bi-controller"></i> 游戏攻略助手</h4>
                </div>
                
                <!-- 游戏类型选择 -->
                <div class="mb-3">
                    <label for="gameType" class="form-label">游戏类型</label>
                    <select class="form-select" id="gameType">
                        <option value="通用游戏">通用游戏</option>
                        <option value="RPG">RPG角色扮演</option>
                        <option value="策略">策略游戏</option>
                        <option value="射击">射击游戏</option>
                        <option value="MOBA">MOBA游戏</option>
                        <option value="卡牌">卡牌游戏</option>
                        <option value="模拟">模拟游戏</option>
                    </select>
                </div>
                
                <!-- 建议问题 -->
                <div class="mb-3">
                    <h6>建议问题</h6>
                    <div id="suggestions" class="suggestions-list">
                        <!-- 动态加载建议问题 -->
                    </div>
                </div>
                
                <!-- 知识库管理 -->
                <div class="mb-3">
                    <h6>知识库管理</h6>
                    <button class="btn btn-sm btn-outline-primary mb-2" onclick="showKnowledgeModal()">
                        <i class="bi bi-database-add"></i> 加载知识库
                    </button>
                    <button class="btn btn-sm btn-outline-info mb-2" onclick="showStatsModal()">
                        <i class="bi bi-bar-chart"></i> 查看统计
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="clearHistory()">
                        <i class="bi bi-trash"></i> 清空历史
                    </button>
                </div>
            </div>
            
            <!-- 主聊天区域 -->
            <div class="col-md-9 main-content">
                <div class="chat-container">
                    <div class="chat-header">
                        <h5><i class="bi bi-chat-dots"></i> 游戏攻略咨询</h5>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <div class="message assistant-message">
                            <div class="message-content">
                                <i class="bi bi-robot"></i>
                                <div class="message-text">
                                    你好！我是游戏攻略小助手，可以帮你解答各种游戏问题。请选择游戏类型并提出你的问题吧！
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="userInput" 
                                   placeholder="请输入你的游戏问题..." onkeypress="handleKeyPress(event)">
                            <button class="btn btn-primary" onclick="sendMessage()">
                                <i class="bi bi-send"></i> 发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 知识库加载模态框 -->
    <div class="modal fade" id="knowledgeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">加载知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="datasetId" class="form-label">数据集ID</label>
                        <input type="text" class="form-control" id="datasetId" 
                               placeholder="请输入魔搭数据集ID">
                    </div>
                    <div class="mb-3">
                        <label for="subsetName" class="form-label">子集名称（可选）</label>
                        <input type="text" class="form-control" id="subsetName" 
                               placeholder="请输入子集名称">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="forceReload">
                        <label class="form-check-label" for="forceReload">
                            强制重新加载
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="loadKnowledge()">加载</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计信息模态框 -->
    <div class="modal fade" id="statsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">系统统计信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="statsContent">
                        <!-- 动态加载统计信息 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
