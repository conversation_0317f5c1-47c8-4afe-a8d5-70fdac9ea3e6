# 游戏攻略小助手

基于Flask + LangChain + DeepSeek API的智能游戏攻略助手，支持从魔搭平台加载知识库，提供个性化的游戏攻略建议。

## 功能特性

- 🎮 **多游戏类型支持**: RPG、MOBA、射击、策略等多种游戏类型
- 🧠 **智能问答**: 基于DeepSeek大语言模型的智能回答
- 📚 **知识库管理**: 支持从魔搭平台加载游戏攻略数据
- 🔍 **语义搜索**: 使用向量数据库进行相似度搜索
- 💬 **对话历史**: 保存和管理对话记录
- 🎨 **现代界面**: 响应式Web界面，支持移动端

## 技术架构

### 后端技术栈
- **Flask**: Web框架
- **LangChain**: AI应用开发框架
- **ChromaDB**: 向量数据库
- **SentenceTransformers**: 文本嵌入模型
- **DeepSeek API**: 大语言模型服务

### 前端技术栈
- **Bootstrap 5**: UI框架
- **JavaScript**: 交互逻辑
- **Bootstrap Icons**: 图标库

## 项目结构

```
LangChainzinengti/
├── app.py                 # Flask主应用
├── config.py             # 配置文件
├── requirements.txt      # 依赖包
├── load_sample_data.py   # 示例数据加载脚本
├── models/
│   ├── llm_client.py    # DeepSeek API客户端
│   └── vector_store.py  # 向量数据库管理
├── services/
│   ├── knowledge_base.py # 知识库服务
│   └── game_assistant.py # 游戏助手逻辑
├── utils/
│   └── data_loader.py   # 数据加载工具
├── data/
│   ├── knowledge_base/  # 知识库文件存储
│   └── chroma_db/       # 向量数据库存储
├── static/
│   ├── css/style.css    # 样式文件
│   └── js/app.js        # 前端脚本
└── templates/
    └── index.html       # 主页模板
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.8+，然后安装依赖：

```bash
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.py` 文件，确认DeepSeek API密钥已正确配置：

```python
DEEPSEEK_API_KEY = "***********************************"
```

### 3. 加载示例数据

运行示例数据加载脚本：

```bash
python load_sample_data.py
```

### 4. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

## 使用指南

### 基本使用

1. **选择游戏类型**: 在左侧边栏选择对应的游戏类型
2. **提出问题**: 在输入框中输入游戏相关问题
3. **获取建议**: 系统会基于知识库提供详细的攻略建议
4. **查看来源**: 可以查看回答的参考资料来源

### 知识库管理

#### 从魔搭平台加载数据

1. 点击"加载知识库"按钮
2. 输入魔搭数据集ID（例如：`your-dataset-id`）
3. 可选择子集名称
4. 点击"加载"开始下载和处理数据

#### 示例代码

```python
from modelscope.msdatasets import MsDataset
from services.knowledge_base import knowledge_base_service

# 加载魔搭数据集
success = knowledge_base_service.load_knowledge_from_modelscope(
    dataset_id="your-dataset-id",
    subset_name="train"  # 可选
)
```

### API接口

#### 问答接口
```
POST /api/ask
Content-Type: application/json

{
    "question": "原神新手怎么玩？",
    "game_type": "RPG"
}
```

#### 加载知识库接口
```
POST /api/knowledge/load
Content-Type: application/json

{
    "dataset_id": "your-dataset-id",
    "subset_name": "train",
    "force_reload": false
}
```

## 配置说明

### 主要配置项

- `DEEPSEEK_API_KEY`: DeepSeek API密钥
- `CHROMA_PERSIST_DIRECTORY`: 向量数据库存储目录
- `EMBEDDING_MODEL`: 文本嵌入模型名称
- `CHUNK_SIZE`: 文本分块大小
- `TEMPERATURE`: 模型温度参数

### 环境变量

可以通过 `.env` 文件设置环境变量：

```
FLASK_ENV=development
SECRET_KEY=your-secret-key
DEEPSEEK_API_KEY=***********************************
```

## 开发指南

### 添加新的游戏类型

1. 在 `templates/index.html` 中添加新的游戏类型选项
2. 在 `services/game_assistant.py` 中添加对应的建议问题
3. 更新相关的提示词模板

### 扩展知识库来源

1. 在 `utils/data_loader.py` 中添加新的数据加载方法
2. 在 `services/knowledge_base.py` 中集成新的加载逻辑
3. 更新API接口支持新的数据源

### 自定义嵌入模型

修改 `config.py` 中的 `EMBEDDING_MODEL` 配置：

```python
EMBEDDING_MODEL = "your-custom-embedding-model"
```

## 故障排除

### 常见问题

1. **向量数据库初始化失败**
   - 检查 `data/chroma_db` 目录权限
   - 确保有足够的磁盘空间

2. **DeepSeek API调用失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 确认API额度是否充足

3. **魔搭数据集下载失败**
   - 检查数据集ID是否正确
   - 确认网络连接稳定
   - 验证魔搭平台访问权限

### 日志调试

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 加入讨论群

---

**注意**: 请确保在使用前正确配置API密钥，并遵守相关服务的使用条款。
