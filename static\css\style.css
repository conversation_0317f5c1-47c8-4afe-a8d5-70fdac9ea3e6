/* 游戏攻略助手样式文件 */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    height: 100vh;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100vh;
    margin: 0;
}

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar .form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.sidebar .form-select {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.sidebar .form-select option {
    background-color: #667eea;
    color: white;
}

.sidebar h6 {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 15px;
    margin-top: 25px;
}

/* 建议问题样式 */
.suggestions-list {
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.suggestion-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

/* 主内容区域 */
.main-content {
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.chat-header {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-header h5 {
    margin: 0;
    font-weight: 600;
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.message {
    margin-bottom: 20px;
    animation: fadeInUp 0.5s ease;
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.user-message .message-content {
    flex-direction: row-reverse;
}

.message-text {
    max-width: 70%;
    padding: 15px 20px;
    border-radius: 20px;
    line-height: 1.5;
    word-wrap: break-word;
}

.user-message .message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.assistant-message .message-text {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.message i {
    font-size: 1.5em;
    margin-top: 5px;
}

.user-message i {
    color: #667eea;
}

.assistant-message i {
    color: #28a745;
}

/* 相关来源样式 */
.sources-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.sources-title {
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
    font-size: 0.9em;
}

.source-item {
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 0 5px 5px 0;
    font-size: 0.85em;
}

.source-meta {
    color: #666;
    font-size: 0.8em;
    margin-top: 5px;
}

/* 输入区域 */
.chat-input {
    padding: 20px;
    background-color: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.chat-input .form-control {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 12px 20px;
    font-size: 1em;
}

.chat-input .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.chat-input .btn {
    border-radius: 25px;
    padding: 12px 25px;
    font-weight: 600;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .message-text {
        max-width: 85%;
    }
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.btn-close {
    filter: invert(1);
}

/* 统计信息样式 */
.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
}

.stats-card h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

.stats-value {
    font-size: 1.5em;
    font-weight: bold;
}
