# 游戏攻略小助手 - 使用指南

## 🎉 恭喜！您的游戏攻略助手已成功搭建

您的基于Flask + LangChain + DeepSeek API的智能游戏攻略助手现在已经完全可用了！

## 📋 当前状态

✅ **Flask应用**: 已启动并运行在 http://localhost:5000  
✅ **向量数据库**: ChromaDB已初始化，包含8条示例游戏攻略  
✅ **DeepSeek API**: 已配置并可用  
✅ **前端界面**: 现代化响应式Web界面已就绪  
✅ **示例数据**: 已加载原神、王者荣耀、我的世界等游戏攻略  

## 🚀 立即开始使用

### 1. 访问Web界面
打开浏览器访问: **http://localhost:5000**

### 2. 基本使用流程
1. **选择游戏类型**: 在左侧边栏选择对应的游戏类型（RPG、MOBA、射击等）
2. **查看建议问题**: 左侧会显示该类型游戏的常见问题
3. **提出问题**: 在底部输入框输入您的游戏问题
4. **获取回答**: 系统会基于知识库和AI模型提供详细回答
5. **查看来源**: 回答下方会显示相关的参考资料来源

### 3. 示例问题
您可以尝试以下问题：
- "原神新手怎么玩？"
- "王者荣耀射手位攻略"
- "我的世界建筑技巧"
- "英雄联盟打野路线"
- "和平精英枪法练习"

## 🔧 功能特性

### 已实现的功能
- ✅ **智能问答**: 基于DeepSeek API的智能回答
- ✅ **语义搜索**: 向量数据库相似度搜索
- ✅ **多游戏支持**: 支持RPG、MOBA、射击、策略等多种游戏类型
- ✅ **对话历史**: 自动保存和管理对话记录
- ✅ **建议问题**: 根据游戏类型提供相关问题建议
- ✅ **知识库管理**: 支持从魔搭平台加载数据
- ✅ **统计信息**: 查看系统使用统计

### 界面功能
- **游戏类型选择**: 左侧边栏可选择不同游戏类型
- **建议问题**: 点击建议问题可快速输入
- **知识库管理**: 点击"加载知识库"可添加新数据
- **统计信息**: 点击"查看统计"了解系统状态
- **清空历史**: 点击"清空历史"重置对话

## 📊 系统信息

### 当前知识库状态
- **文档数量**: 8条示例游戏攻略
- **游戏覆盖**: 原神、王者荣耀、我的世界、英雄联盟、和平精英、炉石传说、文明6、模拟人生4
- **存储位置**: `./data/chroma_db/`

### API接口
- **问答接口**: `POST /api/ask`
- **建议问题**: `GET /api/suggestions`
- **对话历史**: `GET /api/history`
- **知识库加载**: `POST /api/knowledge/load`
- **统计信息**: `GET /api/assistant/stats`

## 🔄 下一步扩展

### 1. 添加更多知识库数据
```python
# 使用魔搭平台数据
knowledge_base_service.load_knowledge_from_modelscope(
    dataset_id="your-dataset-id",
    subset_name="train"
)

# 或添加本地文件
knowledge_base_service.load_knowledge_from_local("./your_data_directory")
```

### 2. 自定义游戏类型
在 `templates/index.html` 中添加新的游戏类型选项，并在 `services/game_assistant.py` 中添加对应的建议问题。

### 3. 优化嵌入模型
当前使用简单的文本嵌入方法，您可以：
- 安装 sentence-transformers 使用专业嵌入模型
- 集成 OpenAI 的 text-embedding-ada-002
- 使用中文优化的嵌入模型

### 4. 部署到生产环境
```bash
# 使用 gunicorn 部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 🛠️ 故障排除

### 常见问题

1. **DeepSeek API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API额度是否充足

2. **向量数据库错误**
   - 检查 `./data/chroma_db/` 目录权限
   - 确保有足够的磁盘空间

3. **前端界面异常**
   - 检查静态文件是否正确加载
   - 查看浏览器控制台错误信息

### 日志查看
Flask应用的日志会显示在终端中，包括：
- API请求记录
- 错误信息
- 系统状态

## 📞 技术支持

如果遇到问题，请检查：
1. Flask应用是否正常运行
2. 依赖包是否正确安装
3. 配置文件是否正确设置
4. 网络连接是否正常

## 🎯 总结

您的游戏攻略小助手现在已经完全可用！这是一个功能完整的AI助手，具备：

- **智能对话**: 基于大语言模型的自然语言理解
- **知识检索**: 向量数据库支持的语义搜索
- **用户友好**: 现代化的Web界面
- **可扩展性**: 模块化设计，易于扩展

开始享受您的智能游戏攻略助手吧！🎮✨
